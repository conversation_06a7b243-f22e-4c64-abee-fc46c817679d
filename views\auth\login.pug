extends ../layout

block content
  .row.justify-content-center
    .col-md-6.col-lg-4
      .card
        .card-header
          h3.text-center Login
        .card-body
          if errors && errors.length > 0
            .alert.alert-danger
              ul.mb-0
                each error in errors
                  li= error.msg
          
          form(method='post', action='/auth/login')
            .mb-3
              label.form-label(for='username') Username
              input.form-control(
                type='text',
                id='username',
                name='username',
                value=formData.username || '',
                required
              )
            
            .mb-3
              label.form-label(for='password') Password
              input.form-control(
                type='password',
                id='password',
                name='password',
                required
              )
            
            .d-grid
              button.btn.btn-primary(type='submit') Login
          
          .text-center.mt-3
            p Don't have an account? 
              a(href='/auth/register') Register here
