extends ../layout

block content
  .row.justify-content-center
    .col-md-8.col-lg-6
      .card
        .card-header
          h3= title
        .card-body
          if errors && errors.length > 0
            .alert.alert-danger
              ul.mb-0
                each error in errors
                  li= error.msg
          
          form(method='post', class='needs-validation', novalidate)
            .mb-3
              label.form-label(for='name') Category Name *
              input.form-control(
                type='text',
                id='name',
                name='name',
                value=category.name || '',
                required,
                maxlength='100'
              )
              .invalid-feedback Please provide a category name.
            
            .mb-3
              label.form-label(for='description') Description
              textarea.form-control(
                id='description',
                name='description',
                rows='3',
                maxlength='500'
              )= category.description || ''
              .form-text Optional description for this category.
            
            .d-flex.gap-2
              button.btn.btn-primary(type='submit') Save Category
              a.btn.btn-secondary(href='/budget/categories') Cancel
