const asyncHandler = require('express-async-handler');
const { DateTime } = require('luxon');

const User = require('../models/user');
const Category = require('../models/category');
const Budget = require('../models/budget');
const Expense = require('../models/expense');

// Display dashboard with summary data
exports.index = asyncHandler(async (req, res, next) => {
  const userId = req.session.user.id;
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  // Get dashboard data in parallel
  const [
    totalExpenses,
    recentExpenses,
    activeBudgets,
    expensesByCategory,
    monthlyTrend
  ] = await Promise.all([
    // Total expenses this month
    Expense.aggregate([
      { $match: { user: userId, date: { $gte: startOfMonth, $lte: endOfMonth } } },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]),

    // Recent expenses (last 5)
    Expense.find({ user: userId })
      .populate('category', 'name')
      .sort({ date: -1 })
      .limit(5),

    // Active budgets
    Budget.find({
      user: userId,
      start_date: { $lte: now },
      end_date: { $gte: now }
    }).populate('category', 'name'),

    // Expenses by category this month
    Expense.aggregate([
      { $match: { user: userId, date: { $gte: startOfMonth, $lte: endOfMonth } } },
      { $group: { _id: '$category', total: { $sum: '$amount' } } },
      { $lookup: { from: 'categories', localField: '_id', foreignField: '_id', as: 'category' } },
      { $unwind: '$category' },
      { $project: { name: '$category.name', amount: '$total' } },
      { $sort: { amount: -1 } }
    ]),

    // Monthly trend (last 6 months)
    Expense.aggregate([
      {
        $match: {
          user: userId,
          date: { $gte: new Date(now.getFullYear(), now.getMonth() - 5, 1) }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$date' },
            month: { $month: '$date' }
          },
          total: { $sum: '$amount' }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1 } }
    ])
  ]);

  // Calculate budget progress
  const budgetProgress = await Promise.all(
    activeBudgets.map(async (budget) => {
      const spent = await Expense.aggregate([
        {
          $match: {
            user: userId,
            category: budget.category._id,
            date: { $gte: budget.start_date, $lte: budget.end_date }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);

      const spentAmount = spent.length > 0 ? spent[0].total : 0;
      const percentage = budget.amount > 0 ? (spentAmount / budget.amount) * 100 : 0;

      return {
        budget,
        spent: spentAmount,
        percentage: Math.min(percentage, 100),
        remaining: Math.max(budget.amount - spentAmount, 0)
      };
    })
  );

  // Format data for charts
  const categoryNames = expensesByCategory.map(item => item.name);
  const categoryAmounts = expensesByCategory.map(item => item.amount);

  const trendLabels = monthlyTrend.map(item => 
    DateTime.fromObject({ year: item._id.year, month: item._id.month }).toFormat('MMM yyyy')
  );
  const trendAmounts = monthlyTrend.map(item => item.total);

  const budgetNames = budgetProgress.map(item => item.budget.name);
  const budgetAmounts = budgetProgress.map(item => item.budget.amount);
  const spentAmounts = budgetProgress.map(item => item.spent);

  res.render('dashboard/index', {
    title: 'Dashboard',
    totalExpenses: totalExpenses.length > 0 ? totalExpenses[0].total : 0,
    recentExpenses,
    budgetProgress,
    expensesByCategory,
    categoryNamesJson: JSON.stringify(categoryNames),
    categoryAmountsJson: JSON.stringify(categoryAmounts),
    trendLabelsJson: JSON.stringify(trendLabels),
    trendAmountsJson: JSON.stringify(trendAmounts),
    budgetNamesJson: JSON.stringify(budgetNames),
    budgetAmountsJson: JSON.stringify(budgetAmounts),
    spentAmountsJson: JSON.stringify(spentAmounts)
  });
});
