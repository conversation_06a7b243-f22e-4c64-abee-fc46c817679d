const asyncHandler = require('express-async-handler');
const { body, validationResult } = require('express-validator');

const Budget = require('../models/budget');
const Category = require('../models/category');
const Expense = require('../models/expense');

// Display list of all Budgets for current user
exports.budget_list = asyncHandler(async (req, res, next) => {
  const budgets = await Budget.find({ user: req.session.user.id })
    .populate('category', 'name')
    .sort({ start_date: -1 });

  // Calculate spent amounts for each budget
  const budgetsWithSpent = await Promise.all(
    budgets.map(async (budget) => {
      const spent = await Expense.aggregate([
        {
          $match: {
            user: budget.user,
            category: budget.category._id,
            date: { $gte: budget.start_date, $lte: budget.end_date }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]);

      const spentAmount = spent.length > 0 ? spent[0].total : 0;
      const percentage = budget.amount > 0 ? (spentAmount / budget.amount) * 100 : 0;

      return {
        ...budget.toObject(),
        spent: spentAmount,
        percentage: Math.min(percentage, 100),
        remaining: Math.max(budget.amount - spentAmount, 0)
      };
    })
  );

  res.render('budget/list', {
    title: 'Budgets',
    budgets: budgetsWithSpent
  });
});

// Display detail page for a specific Budget
exports.budget_detail = asyncHandler(async (req, res, next) => {
  const [budget, expenses] = await Promise.all([
    Budget.findOne({ _id: req.params.id, user: req.session.user.id })
      .populate('category', 'name'),
    Expense.find({
      category: req.params.id,
      user: req.session.user.id,
      date: { $gte: new Date(), $lte: new Date() } // Will be updated with actual budget dates
    }).sort({ date: -1 })
  ]);

  if (!budget) {
    const err = new Error('Budget not found');
    err.status = 404;
    return next(err);
  }

  // Get expenses within budget period
  const budgetExpenses = await Expense.find({
    category: budget.category._id,
    user: req.session.user.id,
    date: { $gte: budget.start_date, $lte: budget.end_date }
  }).sort({ date: -1 });

  // Calculate spent amount
  const spentAmount = budgetExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  const percentage = budget.amount > 0 ? (spentAmount / budget.amount) * 100 : 0;

  res.render('budget/detail', {
    title: `Budget: ${budget.name}`,
    budget,
    expenses: budgetExpenses,
    spent: spentAmount,
    percentage: Math.min(percentage, 100),
    remaining: Math.max(budget.amount - spentAmount, 0)
  });
});

// Display Budget create form on GET
exports.budget_create_get = asyncHandler(async (req, res, next) => {
  const categories = await Category.find({ user: req.session.user.id })
    .sort({ name: 1 });

  res.render('budget/form', {
    title: 'Create Budget',
    budget: {},
    categories,
    errors: []
  });
});

// Handle Budget create on POST
exports.budget_create_post = [
  // Validate and sanitize fields
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Budget name must be between 1 and 100 characters')
    .escape(),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('start_date')
    .isISO8601()
    .withMessage('Start date must be a valid date')
    .toDate(),
  body('end_date')
    .isISO8601()
    .withMessage('End date must be a valid date')
    .toDate()
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.start_date)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  body('category')
    .isMongoId()
    .withMessage('Please select a valid category'),

  // Process request after validation and sanitization
  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);
    const categories = await Category.find({ user: req.session.user.id })
      .sort({ name: 1 });

    // Verify category belongs to user
    const category = await Category.findOne({
      _id: req.body.category,
      user: req.session.user.id
    });

    if (!category) {
      errors.errors.push({ msg: 'Invalid category selected' });
    }

    // Create Budget object with escaped and trimmed data
    const budget = new Budget({
      name: req.body.name,
      amount: req.body.amount,
      start_date: req.body.start_date,
      end_date: req.body.end_date,
      category: req.body.category,
      user: req.session.user.id
    });

    if (!errors.isEmpty()) {
      // There are errors. Render form again with sanitized values/error messages
      res.render('budget/form', {
        title: 'Create Budget',
        budget,
        categories,
        errors: errors.array()
      });
      return;
    }

    // Data is valid, save budget
    await budget.save();
    res.redirect(budget.url);
  })
];

// Display Budget delete form on GET
exports.budget_delete_get = asyncHandler(async (req, res, next) => {
  const budget = await Budget.findOne({ 
    _id: req.params.id, 
    user: req.session.user.id 
  }).populate('category', 'name');

  if (!budget) {
    res.redirect('/budget/budgets');
    return;
  }

  res.render('budget/delete', {
    title: 'Delete Budget',
    budget
  });
});

// Handle Budget delete on POST
exports.budget_delete_post = asyncHandler(async (req, res, next) => {
  const budget = await Budget.findOne({ 
    _id: req.params.id, 
    user: req.session.user.id 
  });

  if (!budget) {
    res.redirect('/budget/budgets');
    return;
  }

  // Delete budget
  await Budget.findByIdAndDelete(req.params.id);
  res.redirect('/budget/budgets');
});

// Display Budget update form on GET
exports.budget_update_get = asyncHandler(async (req, res, next) => {
  const [budget, categories] = await Promise.all([
    Budget.findOne({ _id: req.params.id, user: req.session.user.id }),
    Category.find({ user: req.session.user.id }).sort({ name: 1 })
  ]);

  if (!budget) {
    const err = new Error('Budget not found');
    err.status = 404;
    return next(err);
  }

  res.render('budget/form', {
    title: 'Update Budget',
    budget,
    categories,
    errors: []
  });
});

// Handle Budget update on POST
exports.budget_update_post = [
  // Validate and sanitize fields
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Budget name must be between 1 and 100 characters')
    .escape(),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('start_date')
    .isISO8601()
    .withMessage('Start date must be a valid date')
    .toDate(),
  body('end_date')
    .isISO8601()
    .withMessage('End date must be a valid date')
    .toDate()
    .custom((value, { req }) => {
      if (new Date(value) <= new Date(req.body.start_date)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  body('category')
    .isMongoId()
    .withMessage('Please select a valid category'),

  // Process request after validation and sanitization
  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);
    const categories = await Category.find({ user: req.session.user.id })
      .sort({ name: 1 });

    // Verify category belongs to user
    const category = await Category.findOne({
      _id: req.body.category,
      user: req.session.user.id
    });

    if (!category) {
      errors.errors.push({ msg: 'Invalid category selected' });
    }

    // Create Budget object with escaped and trimmed data (and the old id!)
    const budget = new Budget({
      name: req.body.name,
      amount: req.body.amount,
      start_date: req.body.start_date,
      end_date: req.body.end_date,
      category: req.body.category,
      user: req.session.user.id,
      _id: req.params.id
    });

    if (!errors.isEmpty()) {
      // There are errors. Render the form again with sanitized values and error messages
      res.render('budget/form', {
        title: 'Update Budget',
        budget,
        categories,
        errors: errors.array()
      });
      return;
    }

    // Data is valid. Update the record
    await Budget.findByIdAndUpdate(req.params.id, budget);
    res.redirect(budget.url);
  })
];
