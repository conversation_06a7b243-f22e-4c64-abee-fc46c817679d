const asyncHandler = require('express-async-handler');
const { body, validationResult } = require('express-validator');

const Category = require('../models/category');
const Budget = require('../models/budget');
const Expense = require('../models/expense');

// Display list of all Categories for current user
exports.category_list = asyncHandler(async (req, res, next) => {
  const categories = await Category.find({ user: req.session.user.id })
    .sort({ name: 1 });

  res.render('category/list', {
    title: 'Categories',
    categories
  });
});

// Display detail page for a specific Category
exports.category_detail = asyncHandler(async (req, res, next) => {
  const [category, budgets, expenses] = await Promise.all([
    Category.findOne({ _id: req.params.id, user: req.session.user.id }),
    Budget.find({ category: req.params.id, user: req.session.user.id })
      .sort({ start_date: -1 }),
    Expense.find({ category: req.params.id, user: req.session.user.id })
      .sort({ date: -1 })
      .limit(10)
  ]);

  if (!category) {
    const err = new Error('Category not found');
    err.status = 404;
    return next(err);
  }

  res.render('category/detail', {
    title: `Category: ${category.name}`,
    category,
    budgets,
    expenses
  });
});

// Display Category create form on GET
exports.category_create_get = (req, res, next) => {
  res.render('category/form', { 
    title: 'Create Category',
    category: {},
    errors: []
  });
};

// Handle Category create on POST
exports.category_create_post = [
  // Validate and sanitize fields
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category name must be between 1 and 100 characters')
    .escape(),
  body('description')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
    .escape(),

  // Process request after validation and sanitization
  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);

    // Create Category object with escaped and trimmed data
    const category = new Category({
      name: req.body.name,
      description: req.body.description,
      user: req.session.user.id
    });

    if (!errors.isEmpty()) {
      // There are errors. Render form again with sanitized values/error messages
      res.render('category/form', {
        title: 'Create Category',
        category,
        errors: errors.array()
      });
      return;
    }

    // Check if category name already exists for this user
    const existingCategory = await Category.findOne({
      name: req.body.name,
      user: req.session.user.id
    });

    if (existingCategory) {
      res.render('category/form', {
        title: 'Create Category',
        category,
        errors: [{ msg: 'Category name already exists' }]
      });
      return;
    }

    // Data is valid, save category
    await category.save();
    res.redirect(category.url);
  })
];

// Display Category delete form on GET
exports.category_delete_get = asyncHandler(async (req, res, next) => {
  const [category, budgets, expenses] = await Promise.all([
    Category.findOne({ _id: req.params.id, user: req.session.user.id }),
    Budget.find({ category: req.params.id, user: req.session.user.id }),
    Expense.find({ category: req.params.id, user: req.session.user.id })
  ]);

  if (!category) {
    res.redirect('/budget/categories');
    return;
  }

  res.render('category/delete', {
    title: 'Delete Category',
    category,
    budgets,
    expenses
  });
});

// Handle Category delete on POST
exports.category_delete_post = asyncHandler(async (req, res, next) => {
  const [category, budgets, expenses] = await Promise.all([
    Category.findOne({ _id: req.params.id, user: req.session.user.id }),
    Budget.find({ category: req.params.id, user: req.session.user.id }),
    Expense.find({ category: req.params.id, user: req.session.user.id })
  ]);

  if (!category) {
    res.redirect('/budget/categories');
    return;
  }

  if (budgets.length > 0 || expenses.length > 0) {
    // Category has budgets or expenses. Render delete page with warning
    res.render('category/delete', {
      title: 'Delete Category',
      category,
      budgets,
      expenses,
      errors: [{ msg: 'Cannot delete category that has associated budgets or expenses' }]
    });
    return;
  }

  // Category has no budgets or expenses. Delete and redirect
  await Category.findByIdAndDelete(req.params.id);
  res.redirect('/budget/categories');
});

// Display Category update form on GET
exports.category_update_get = asyncHandler(async (req, res, next) => {
  const category = await Category.findOne({ 
    _id: req.params.id, 
    user: req.session.user.id 
  });

  if (!category) {
    const err = new Error('Category not found');
    err.status = 404;
    return next(err);
  }

  res.render('category/form', { 
    title: 'Update Category',
    category,
    errors: []
  });
});

// Handle Category update on POST
exports.category_update_post = [
  // Validate and sanitize fields
  body('name')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Category name must be between 1 and 100 characters')
    .escape(),
  body('description')
    .optional({ checkFalsy: true })
    .trim()
    .isLength({ max: 500 })
    .withMessage('Description must not exceed 500 characters')
    .escape(),

  // Process request after validation and sanitization
  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);

    // Create Category object with escaped and trimmed data (and the old id!)
    const category = new Category({
      name: req.body.name,
      description: req.body.description,
      user: req.session.user.id,
      _id: req.params.id
    });

    if (!errors.isEmpty()) {
      // There are errors. Render the form again with sanitized values and error messages
      res.render('category/form', {
        title: 'Update Category',
        category,
        errors: errors.array()
      });
      return;
    }

    // Check if category name already exists for this user (excluding current category)
    const existingCategory = await Category.findOne({
      name: req.body.name,
      user: req.session.user.id,
      _id: { $ne: req.params.id }
    });

    if (existingCategory) {
      res.render('category/form', {
        title: 'Update Category',
        category,
        errors: [{ msg: 'Category name already exists' }]
      });
      return;
    }

    // Data is valid. Update the record
    await Category.findByIdAndUpdate(req.params.id, category);
    res.redirect(category.url);
  })
];
