const asyncHandler = require('express-async-handler');
const { body, validationResult } = require('express-validator');

const Expense = require('../models/expense');
const Category = require('../models/category');

// Display list of all Expenses for current user
exports.expense_list = asyncHandler(async (req, res, next) => {
  const expenses = await Expense.find({ user: req.session.user.id })
    .populate('category', 'name')
    .sort({ date: -1 });

  res.render('expense/list', {
    title: 'Expenses',
    expenses
  });
});

// Display detail page for a specific Expense
exports.expense_detail = asyncHandler(async (req, res, next) => {
  const expense = await Expense.findOne({ 
    _id: req.params.id, 
    user: req.session.user.id 
  }).populate('category', 'name');

  if (!expense) {
    const err = new Error('Expense not found');
    err.status = 404;
    return next(err);
  }

  res.render('expense/detail', {
    title: `Expense: ${expense.description}`,
    expense
  });
});

// Display Expense create form on GET
exports.expense_create_get = asyncHandler(async (req, res, next) => {
  const categories = await Category.find({ user: req.session.user.id })
    .sort({ name: 1 });

  res.render('expense/form', {
    title: 'Add Expense',
    expense: { date: new Date() }, // Default to today
    categories,
    errors: []
  });
});

// Handle Expense create on POST
exports.expense_create_post = [
  // Validate and sanitize fields
  body('description')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Description must be between 1 and 200 characters')
    .escape(),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('date')
    .isISO8601()
    .withMessage('Date must be a valid date')
    .toDate(),
  body('category')
    .isMongoId()
    .withMessage('Please select a valid category'),

  // Process request after validation and sanitization
  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);
    const categories = await Category.find({ user: req.session.user.id })
      .sort({ name: 1 });

    // Verify category belongs to user
    const category = await Category.findOne({
      _id: req.body.category,
      user: req.session.user.id
    });

    if (!category) {
      errors.errors.push({ msg: 'Invalid category selected' });
    }

    // Create Expense object with escaped and trimmed data
    const expense = new Expense({
      description: req.body.description,
      amount: req.body.amount,
      date: req.body.date,
      category: req.body.category,
      user: req.session.user.id
    });

    if (!errors.isEmpty()) {
      // There are errors. Render form again with sanitized values/error messages
      res.render('expense/form', {
        title: 'Add Expense',
        expense,
        categories,
        errors: errors.array()
      });
      return;
    }

    // Data is valid, save expense
    await expense.save();
    res.redirect(expense.url);
  })
];

// Display Expense delete form on GET
exports.expense_delete_get = asyncHandler(async (req, res, next) => {
  const expense = await Expense.findOne({ 
    _id: req.params.id, 
    user: req.session.user.id 
  }).populate('category', 'name');

  if (!expense) {
    res.redirect('/budget/expenses');
    return;
  }

  res.render('expense/delete', {
    title: 'Delete Expense',
    expense
  });
});

// Handle Expense delete on POST
exports.expense_delete_post = asyncHandler(async (req, res, next) => {
  const expense = await Expense.findOne({ 
    _id: req.params.id, 
    user: req.session.user.id 
  });

  if (!expense) {
    res.redirect('/budget/expenses');
    return;
  }

  // Delete expense
  await Expense.findByIdAndDelete(req.params.id);
  res.redirect('/budget/expenses');
});

// Display Expense update form on GET
exports.expense_update_get = asyncHandler(async (req, res, next) => {
  const [expense, categories] = await Promise.all([
    Expense.findOne({ _id: req.params.id, user: req.session.user.id }),
    Category.find({ user: req.session.user.id }).sort({ name: 1 })
  ]);

  if (!expense) {
    const err = new Error('Expense not found');
    err.status = 404;
    return next(err);
  }

  res.render('expense/form', {
    title: 'Update Expense',
    expense,
    categories,
    errors: []
  });
});

// Handle Expense update on POST
exports.expense_update_post = [
  // Validate and sanitize fields
  body('description')
    .trim()
    .isLength({ min: 1, max: 200 })
    .withMessage('Description must be between 1 and 200 characters')
    .escape(),
  body('amount')
    .isFloat({ min: 0 })
    .withMessage('Amount must be a positive number'),
  body('date')
    .isISO8601()
    .withMessage('Date must be a valid date')
    .toDate(),
  body('category')
    .isMongoId()
    .withMessage('Please select a valid category'),

  // Process request after validation and sanitization
  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);
    const categories = await Category.find({ user: req.session.user.id })
      .sort({ name: 1 });

    // Verify category belongs to user
    const category = await Category.findOne({
      _id: req.body.category,
      user: req.session.user.id
    });

    if (!category) {
      errors.errors.push({ msg: 'Invalid category selected' });
    }

    // Create Expense object with escaped and trimmed data (and the old id!)
    const expense = new Expense({
      description: req.body.description,
      amount: req.body.amount,
      date: req.body.date,
      category: req.body.category,
      user: req.session.user.id,
      _id: req.params.id
    });

    if (!errors.isEmpty()) {
      // There are errors. Render the form again with sanitized values and error messages
      res.render('expense/form', {
        title: 'Update Expense',
        expense,
        categories,
        errors: errors.array()
      });
      return;
    }

    // Data is valid. Update the record
    await Expense.findByIdAndUpdate(req.params.id, expense);
    res.redirect(expense.url);
  })
];
