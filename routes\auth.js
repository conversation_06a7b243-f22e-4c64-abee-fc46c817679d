const express = require('express');
const router = express.Router();
const asyncHandler = require('express-async-handler');
const { body, validationResult } = require('express-validator');

const User = require('../models/user');
const { redirectIfAuthenticated } = require('../middleware/auth');

// GET login page
router.get('/login', redirectIfAuthenticated, (req, res) => {
  res.render('auth/login', { 
    title: 'Login',
    errors: [],
    formData: {}
  });
});

// POST login
router.post('/login', [
  body('username')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Username is required'),
  body('password')
    .isLength({ min: 1 })
    .withMessage('Password is required'),

  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);
    const { username, password } = req.body;

    if (!errors.isEmpty()) {
      return res.render('auth/login', {
        title: 'Login',
        errors: errors.array(),
        formData: { username }
      });
    }

    try {
      const user = await User.findOne({ username: username.toLowerCase() });
      
      if (!user || !(await user.checkPassword(password))) {
        return res.render('auth/login', {
          title: 'Login',
          errors: [{ msg: 'Invalid username or password' }],
          formData: { username }
        });
      }

      // Set session
      req.session.user = {
        id: user._id,
        username: user.username
      };

      // Redirect to intended page or dashboard
      const redirectTo = req.session.returnTo || '/budget/dashboard';
      delete req.session.returnTo;
      res.redirect(redirectTo);

    } catch (error) {
      next(error);
    }
  })
]);

// GET register page
router.get('/register', redirectIfAuthenticated, (req, res) => {
  res.render('auth/register', { 
    title: 'Register',
    errors: [],
    formData: {}
  });
});

// POST register
router.post('/register', [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .isAlphanumeric()
    .withMessage('Username must contain only letters and numbers'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long'),
  body('confirm_password')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('Passwords do not match');
      }
      return true;
    }),

  asyncHandler(async (req, res, next) => {
    const errors = validationResult(req);
    const { username, password } = req.body;

    // Check if username already exists
    const existingUser = await User.findOne({ username: username.toLowerCase() });
    if (existingUser) {
      errors.errors.push({ msg: 'Username already exists' });
    }

    if (!errors.isEmpty()) {
      return res.render('auth/register', {
        title: 'Register',
        errors: errors.array(),
        formData: { username }
      });
    }

    try {
      const hashedPassword = await User.hashPassword(password);
      const user = new User({
        username: username.toLowerCase(),
        password_hash: hashedPassword
      });

      await user.save();

      // Set session
      req.session.user = {
        id: user._id,
        username: user.username
      };

      res.redirect('/budget/dashboard');

    } catch (error) {
      next(error);
    }
  })
]);

// POST logout
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.error('Error destroying session:', err);
    }
    res.redirect('/auth/login');
  });
});

module.exports = router;
