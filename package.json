{"name": "express-budget-tracker", "version": "1.0.0", "private": true, "description": "Personal Budget Tracker built with Express.js", "engines": {"node": ">=16.0.0"}, "scripts": {"start": "node ./bin/www", "devstart": "nodemon ./bin/www", "serverstart": "SET DEBUG=express-budget-tracker:* & npm run devstart", "initdb": "node ./scripts/initdb.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "cookie-parser": "^1.4.6", "debug": "^4.3.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "http-errors": "~2.0.0", "luxon": "^3.6.1", "mongodb": "^5.9.2", "mongoose": "^8.14.3", "morgan": "^1.10.0", "pug": "^3.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}, "keywords": ["budget", "tracker", "expense", "finance", "express", "mongodb"], "author": "Budget Tracker Team", "license": "MIT"}