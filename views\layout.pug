doctype html
html(lang='en')
  head
    meta(charset='UTF-8')
    meta(name='viewport', content='width=device-width, initial-scale=1.0')
    title= title + ' - Budget Tracker'
    link(rel='stylesheet', href='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css')
    link(rel='stylesheet', href='https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css')
    link(rel='stylesheet', href='/stylesheets/style.css')
    script(src='https://cdn.jsdelivr.net/npm/chart.js')
  
  body
    nav.navbar.navbar-expand-lg.navbar-dark.bg-primary
      .container
        a.navbar-brand(href='/') Budget Tracker
        
        if user
          button.navbar-toggler(type='button', data-bs-toggle='collapse', data-bs-target='#navbarNav')
            span.navbar-toggler-icon
          
          .collapse.navbar-collapse#navbarNav
            ul.navbar-nav.me-auto
              li.nav-item
                a.nav-link(href='/budget/dashboard') Dashboard
              li.nav-item.dropdown
                a.nav-link.dropdown-toggle(href='#', role='button', data-bs-toggle='dropdown') Expenses
                ul.dropdown-menu
                  li: a.dropdown-item(href='/budget/expenses') View All
                  li: a.dropdown-item(href='/budget/expense/create') Add New
              li.nav-item.dropdown
                a.nav-link.dropdown-toggle(href='#', role='button', data-bs-toggle='dropdown') Budgets
                ul.dropdown-menu
                  li: a.dropdown-item(href='/budget/budgets') View All
                  li: a.dropdown-item(href='/budget/budget/create') Create New
              li.nav-item.dropdown
                a.nav-link.dropdown-toggle(href='#', role='button', data-bs-toggle='dropdown') Categories
                ul.dropdown-menu
                  li: a.dropdown-item(href='/budget/categories') View All
                  li: a.dropdown-item(href='/budget/category/create') Create New
            
            ul.navbar-nav
              li.nav-item.dropdown
                a.nav-link.dropdown-toggle(href='#', role='button', data-bs-toggle='dropdown')
                  i.bi.bi-person-circle
                  |  #{user.username}
                ul.dropdown-menu
                  li
                    form(method='post', action='/auth/logout')
                      button.dropdown-item(type='submit') Logout

    main.container.mt-4
      if locals.messages
        each message in messages
          .alert.alert-info.alert-dismissible.fade.show
            = message
            button.type('button').btn-close(data-bs-dismiss='alert')
      
      block content

    footer.bg-light.text-center.py-3.mt-5
      .container
        p.mb-0 &copy; 2024 Budget Tracker. Built with Express.js and MongoDB.

    script(src='https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js')
    script(src='/javascripts/app.js')
