# Express Budget Tracker

A personal budget tracking application built with Express.js, following the design principles from the Express Local Library tutorial. This application was migrated from a Flask-based budget tracker to demonstrate Express.js best practices.

## Features

- **User Authentication**: Secure registration and login system
- **Expense Tracking**: Add, edit, and delete expenses with categorization
- **Budget Management**: Create and monitor budgets with progress tracking
- **Category Management**: Organize expenses and budgets by categories
- **Dashboard Analytics**: Visual charts and summaries of spending patterns
- **Responsive Design**: Mobile-friendly interface using Bootstrap

## Technology Stack

- **Backend**: Node.js with Express.js framework
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: Express sessions with bcryptjs password hashing
- **Validation**: express-validator for form validation and sanitization
- **Templating**: Pug template engine
- **Frontend**: Bootstrap 5 with Chart.js for data visualization
- **Development**: Nodemon for auto-restart during development

## Architecture

This application follows the MVC (Model-View-Controller) pattern:

- **Models** (`/models`): Mongoose schemas for User, Category, Budget, and Expense
- **Views** (`/views`): Pug templates organized by feature
- **Controllers** (`/controllers`): Business logic separated by entity
- **Routes** (`/routes`): Express routers for different application areas
- **Middleware** (`/middleware`): Custom authentication and utility middleware

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd express-budget-tracker
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   ```
   Edit `.env` with your configuration:
   - `MONGODB_URI`: Your MongoDB connection string
   - `SESSION_SECRET`: A secure random string for session encryption
   - `PORT`: Application port (default: 3000)

4. **Start MongoDB**:
   Make sure MongoDB is running on your system or use a cloud service like MongoDB Atlas.

5. **Run the application**:
   ```bash
   # Development mode with auto-restart
   npm run devstart
   
   # Production mode
   npm start
   ```

6. **Access the application**:
   Open your browser and navigate to `http://localhost:3000`

## Usage

1. **Register**: Create a new account with a unique username
2. **Create Categories**: Set up expense categories (e.g., Food, Transportation, Entertainment)
3. **Add Budgets**: Create budgets for different categories with date ranges
4. **Track Expenses**: Record your daily expenses and assign them to categories
5. **Monitor Progress**: View your dashboard for spending analysis and budget progress

## Project Structure

```
express-budget-tracker/
├── bin/
│   └── www                 # Application entry point
├── controllers/            # Business logic controllers
│   ├── dashboardController.js
│   ├── categoryController.js
│   ├── budgetController.js
│   └── expenseController.js
├── middleware/             # Custom middleware
│   └── auth.js            # Authentication middleware
├── models/                # Mongoose data models
│   ├── user.js
│   ├── category.js
│   ├── budget.js
│   └── expense.js
├── public/                # Static assets
│   ├── stylesheets/
│   └── javascripts/
├── routes/                # Express route definitions
│   ├── index.js
│   ├── auth.js
│   └── budget.js
├── views/                 # Pug templates
│   ├── auth/
│   ├── dashboard/
│   ├── category/
│   ├── budget/
│   ├── expense/
│   ├── layout.pug
│   └── error.pug
├── app.js                 # Express application setup
├── package.json
└── README.md
```

## Key Design Principles Applied

This migration demonstrates several Express.js best practices:

1. **Separation of Concerns**: Clear separation between routes, controllers, and models
2. **Middleware Pattern**: Custom authentication middleware and validation chains
3. **Error Handling**: Centralized error handling with custom error pages
4. **Security**: Password hashing, input validation, and session management
5. **Modularity**: Feature-based organization with reusable components
6. **Async/Await**: Modern JavaScript patterns with proper error handling

## Development

- **Linting**: Add ESLint configuration for code quality
- **Testing**: Implement unit and integration tests
- **Logging**: Add structured logging with Winston
- **API**: Consider adding REST API endpoints for mobile apps

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Based on the Express Local Library tutorial architecture
- Migrated from a Flask-based budget tracker application
- Uses Chart.js for data visualization
- Bootstrap for responsive design
