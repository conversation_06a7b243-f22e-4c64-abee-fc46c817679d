extends ../layout

block content
  .d-flex.justify-content-between.align-items-center.mb-4
    h1= title
    a.btn.btn-primary(href='/budget/category/create')
      i.bi.bi-plus-circle.me-2
      | Add Category

  if categories && categories.length > 0
    .row
      each category in categories
        .col-md-6.col-lg-4.mb-3
          .card
            .card-body
              h5.card-title= category.name
              if category.description
                p.card-text.text-muted= category.description
              else
                p.card-text.text-muted.fst-italic No description
              
              .d-flex.gap-2
                a.btn.btn-sm.btn-outline-primary(href=category.url) View
                a.btn.btn-sm.btn-outline-secondary(href=category.edit_url) Edit
                a.btn.btn-sm.btn-outline-danger.btn-delete(href=category.delete_url) Delete
  else
    .card
      .card-body.text-center
        i.bi.bi-tags.display-1.text-muted.mb-3
        h4.text-muted No categories yet
        p.text-muted Create your first category to start organizing your expenses.
        a.btn.btn-primary(href='/budget/category/create') Create Category
