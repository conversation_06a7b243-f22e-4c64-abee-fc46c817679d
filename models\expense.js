const mongoose = require('mongoose');
const { DateTime } = require('luxon');

const Schema = mongoose.Schema;

const ExpenseSchema = new Schema({
  description: {
    type: String,
    required: true,
    trim: true,
    maxLength: 200
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Virtual for expense's URL
ExpenseSchema.virtual('url').get(function () {
  return `/budget/expense/${this._id}`;
});

// Virtual for expense's edit URL
ExpenseSchema.virtual('edit_url').get(function () {
  return `/budget/expense/${this._id}/edit`;
});

// Virtual for expense's delete URL
ExpenseSchema.virtual('delete_url').get(function () {
  return `/budget/expense/${this._id}/delete`;
});

// Virtual for formatted date
ExpenseSchema.virtual('date_formatted').get(function () {
  return this.date ? DateTime.fromJSDate(this.date).toLocaleString(DateTime.DATE_MED) : '';
});

// Virtual for formatted amount
ExpenseSchema.virtual('amount_formatted').get(function () {
  return `$${this.amount.toFixed(2)}`;
});

// Virtual for date input format (YYYY-MM-DD)
ExpenseSchema.virtual('date_input_format').get(function () {
  return this.date ? DateTime.fromJSDate(this.date).toISODate() : '';
});

// Index for efficient querying
ExpenseSchema.index({ user: 1, date: -1 });
ExpenseSchema.index({ category: 1 });
ExpenseSchema.index({ user: 1, category: 1, date: -1 });

// Export model
module.exports = mongoose.model('Expense', ExpenseSchema);
