extends ../layout

block content
  .row
    .col-12
      h1.mb-4 Dashboard
      
      // Summary Cards
      .row.mb-4
        .col-md-3
          .card.bg-primary.text-white
            .card-body
              .d-flex.justify-content-between
                div
                  h5.card-title This Month's Expenses
                  h3.mb-0 $#{totalExpenses.toFixed(2)}
                .align-self-center
                  i.bi.bi-cash-stack.fs-1
        
        .col-md-3
          .card.bg-success.text-white
            .card-body
              .d-flex.justify-content-between
                div
                  h5.card-title Active Budgets
                  h3.mb-0= budgetProgress.length
                .align-self-center
                  i.bi.bi-piggy-bank.fs-1
        
        .col-md-3
          .card.bg-info.text-white
            .card-body
              .d-flex.justify-content-between
                div
                  h5.card-title Recent Expenses
                  h3.mb-0= recentExpenses.length
                .align-self-center
                  i.bi.bi-receipt.fs-1
        
        .col-md-3
          .card.bg-warning.text-white
            .card-body
              .d-flex.justify-content-between
                div
                  h5.card-title Categories
                  h3.mb-0= expensesByCategory.length
                .align-self-center
                  i.bi.bi-tags.fs-1

  .row
    // Recent Expenses
    .col-md-6
      .card
        .card-header
          .d-flex.justify-content-between.align-items-center
            h5.mb-0 Recent Expenses
            a.btn.btn-sm.btn-outline-primary(href='/budget/expenses') View All
        .card-body
          if recentExpenses.length > 0
            .list-group.list-group-flush
              each expense in recentExpenses
                .list-group-item.d-flex.justify-content-between.align-items-center
                  div
                    strong= expense.description
                    br
                    small.text-muted= expense.date_formatted + ' • ' + expense.category.name
                  span.badge.bg-primary.rounded-pill $#{expense.amount.toFixed(2)}
          else
            p.text-muted No expenses recorded yet.
    
    // Budget Progress
    .col-md-6
      .card
        .card-header
          .d-flex.justify-content-between.align-items-center
            h5.mb-0 Budget Progress
            a.btn.btn-sm.btn-outline-primary(href='/budget/budgets') View All
        .card-body
          if budgetProgress.length > 0
            each budget in budgetProgress
              .mb-3
                .d-flex.justify-content-between.align-items-center.mb-1
                  span= budget.budget.name
                  span.text-muted $#{budget.spent.toFixed(2)} / $#{budget.budget.amount.toFixed(2)}
                .progress
                  .progress-bar(
                    class=budget.percentage > 90 ? 'bg-danger' : budget.percentage > 75 ? 'bg-warning' : 'bg-success',
                    style=`width: ${budget.percentage}%`
                  )
          else
            p.text-muted No active budgets.

  // Charts Row
  .row.mt-4
    .col-md-6
      .card
        .card-header
          h5.mb-0 Expenses by Category
        .card-body
          if expensesByCategory.length > 0
            canvas#categoryChart(width='400', height='200')
          else
            p.text-muted No expense data available.
    
    .col-md-6
      .card
        .card-header
          h5.mb-0 Monthly Trend
        .card-body
          canvas#trendChart(width='400', height='200')

  script.
    // Category Chart
    if (#{categoryNamesJson}.length > 0) {
      const categoryCtx = document.getElementById('categoryChart').getContext('2d');
      new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
          labels: !{categoryNamesJson},
          datasets: [{
            data: !{categoryAmountsJson},
            backgroundColor: [
              '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
              '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
            ]
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      });
    }

    // Trend Chart
    const trendCtx = document.getElementById('trendChart').getContext('2d');
    new Chart(trendCtx, {
      type: 'line',
      data: {
        labels: !{trendLabelsJson},
        datasets: [{
          label: 'Monthly Expenses',
          data: !{trendAmountsJson},
          borderColor: '#36A2EB',
          backgroundColor: 'rgba(54, 162, 235, 0.1)',
          tension: 0.4
        }]
      },
      options: {
        responsive: true,
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '$' + value.toFixed(2);
              }
            }
          }
        }
      }
    });
