#!/usr/bin/env node

/**
 * Database initialization script
 * Creates sample data for testing the budget tracker application
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../models/user');
const Category = require('../models/category');
const Budget = require('../models/budget');
const Expense = require('../models/expense');

const mongoDB = process.env.MONGODB_URI || 'mongodb://localhost:27017/budget_tracker';

async function initializeDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(mongoDB);
    console.log('Connected to MongoDB');

    // Clear existing data
    await Promise.all([
      User.deleteMany({}),
      Category.deleteMany({}),
      Budget.deleteMany({}),
      Expense.deleteMany({})
    ]);
    console.log('Cleared existing data');

    // Create sample user
    const hashedPassword = await User.hashPassword('password123');
    const user = new User({
      username: 'demo',
      password_hash: hashedPassword
    });
    await user.save();
    console.log('Created demo user (username: demo, password: password123)');

    // Create sample categories
    const categories = await Category.create([
      { name: 'Food & Dining', description: 'Restaurants, groceries, and food delivery', user: user._id },
      { name: 'Transportation', description: 'Gas, public transport, car maintenance', user: user._id },
      { name: 'Entertainment', description: 'Movies, games, subscriptions', user: user._id },
      { name: 'Shopping', description: 'Clothing, electronics, household items', user: user._id },
      { name: 'Bills & Utilities', description: 'Rent, electricity, internet, phone', user: user._id },
      { name: 'Healthcare', description: 'Medical expenses, pharmacy, insurance', user: user._id }
    ]);
    console.log('Created sample categories');

    // Create sample budgets
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    const budgets = await Budget.create([
      {
        name: 'Monthly Food Budget',
        amount: 500,
        start_date: startOfMonth,
        end_date: endOfMonth,
        category: categories[0]._id,
        user: user._id
      },
      {
        name: 'Transportation Budget',
        amount: 200,
        start_date: startOfMonth,
        end_date: endOfMonth,
        category: categories[1]._id,
        user: user._id
      },
      {
        name: 'Entertainment Budget',
        amount: 150,
        start_date: startOfMonth,
        end_date: endOfMonth,
        category: categories[2]._id,
        user: user._id
      }
    ]);
    console.log('Created sample budgets');

    // Create sample expenses
    const expenses = [];
    const expenseData = [
      { description: 'Grocery shopping', amount: 85.50, category: categories[0]._id, daysAgo: 1 },
      { description: 'Gas station', amount: 45.00, category: categories[1]._id, daysAgo: 2 },
      { description: 'Netflix subscription', amount: 15.99, category: categories[2]._id, daysAgo: 3 },
      { description: 'Lunch at restaurant', amount: 25.75, category: categories[0]._id, daysAgo: 3 },
      { description: 'Coffee shop', amount: 8.50, category: categories[0]._id, daysAgo: 4 },
      { description: 'Movie tickets', amount: 24.00, category: categories[2]._id, daysAgo: 5 },
      { description: 'Pharmacy', amount: 32.15, category: categories[5]._id, daysAgo: 6 },
      { description: 'Online shopping', amount: 67.99, category: categories[3]._id, daysAgo: 7 },
      { description: 'Bus fare', amount: 12.50, category: categories[1]._id, daysAgo: 8 },
      { description: 'Takeout dinner', amount: 35.25, category: categories[0]._id, daysAgo: 9 }
    ];

    for (const expenseInfo of expenseData) {
      const expenseDate = new Date();
      expenseDate.setDate(expenseDate.getDate() - expenseInfo.daysAgo);
      
      expenses.push({
        description: expenseInfo.description,
        amount: expenseInfo.amount,
        date: expenseDate,
        category: expenseInfo.category,
        user: user._id
      });
    }

    await Expense.create(expenses);
    console.log('Created sample expenses');

    console.log('\n✅ Database initialization completed successfully!');
    console.log('\nYou can now log in with:');
    console.log('Username: demo');
    console.log('Password: password123');
    console.log('\nStart the application with: npm run devstart');

  } catch (error) {
    console.error('Error initializing database:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the initialization
initializeDatabase();
