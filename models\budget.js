const mongoose = require('mongoose');
const { DateTime } = require('luxon');

const Schema = mongoose.Schema;

const BudgetSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxLength: 100
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  start_date: {
    type: Date,
    required: true
  },
  end_date: {
    type: Date,
    required: true
  },
  category: {
    type: Schema.Types.ObjectId,
    ref: 'Category',
    required: true
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Virtual for budget's URL
BudgetSchema.virtual('url').get(function () {
  return `/budget/budget/${this._id}`;
});

// Virtual for budget's edit URL
BudgetSchema.virtual('edit_url').get(function () {
  return `/budget/budget/${this._id}/edit`;
});

// Virtual for budget's delete URL
BudgetSchema.virtual('delete_url').get(function () {
  return `/budget/budget/${this._id}/delete`;
});

// Virtual for formatted start date
BudgetSchema.virtual('start_date_formatted').get(function () {
  return this.start_date ? DateTime.fromJSDate(this.start_date).toLocaleString(DateTime.DATE_MED) : '';
});

// Virtual for formatted end date
BudgetSchema.virtual('end_date_formatted').get(function () {
  return this.end_date ? DateTime.fromJSDate(this.end_date).toLocaleString(DateTime.DATE_MED) : '';
});

// Virtual for formatted amount
BudgetSchema.virtual('amount_formatted').get(function () {
  return `$${this.amount.toFixed(2)}`;
});

// Virtual to check if budget is active
BudgetSchema.virtual('is_active').get(function () {
  const now = new Date();
  return this.start_date <= now && this.end_date >= now;
});

// Index for efficient querying
BudgetSchema.index({ user: 1, start_date: -1 });
BudgetSchema.index({ category: 1 });

// Export model
module.exports = mongoose.model('Budget', BudgetSchema);
