extends ../layout

block content
  .row.justify-content-center
    .col-md-6.col-lg-4
      .card
        .card-header
          h3.text-center Register
        .card-body
          if errors && errors.length > 0
            .alert.alert-danger
              ul.mb-0
                each error in errors
                  li= error.msg
          
          form(method='post', action='/auth/register')
            .mb-3
              label.form-label(for='username') Username
              input.form-control(
                type='text',
                id='username',
                name='username',
                value=formData.username || '',
                required,
                minlength='3',
                maxlength='20'
              )
              .form-text Username must be 3-20 characters, letters and numbers only
            
            .mb-3
              label.form-label(for='password') Password
              input.form-control(
                type='password',
                id='password',
                name='password',
                required,
                minlength='6'
              )
              .form-text Password must be at least 6 characters
            
            .mb-3
              label.form-label(for='confirm_password') Confirm Password
              input.form-control(
                type='password',
                id='confirm_password',
                name='confirm_password',
                required
              )
            
            .d-grid
              button.btn.btn-primary(type='submit') Register
          
          .text-center.mt-3
            p Already have an account? 
              a(href='/auth/login') Login here
