extends ../layout

block content
  .row.justify-content-center
    .col-md-8.col-lg-6
      .card
        .card-header
          h3= title
        .card-body
          if errors && errors.length > 0
            .alert.alert-danger
              ul.mb-0
                each error in errors
                  li= error.msg
          
          form(method='post', class='needs-validation', novalidate)
            .mb-3
              label.form-label(for='description') Description *
              input.form-control(
                type='text',
                id='description',
                name='description',
                value=expense.description || '',
                required,
                maxlength='200',
                placeholder='e.g., Lunch at restaurant'
              )
              .invalid-feedback Please provide an expense description.
            
            .mb-3
              label.form-label(for='amount') Amount *
              .input-group
                span.input-group-text $
                input.form-control(
                  type='number',
                  id='amount',
                  name='amount',
                  value=expense.amount || '',
                  required,
                  min='0',
                  step='0.01',
                  placeholder='0.00'
                )
              .invalid-feedback Please provide a valid amount.
            
            .mb-3
              label.form-label(for='date') Date *
              input.form-control(
                type='date',
                id='date',
                name='date',
                value=expense.date_input_format || new Date().toISOString().split('T')[0],
                required
              )
              .invalid-feedback Please select a date.
            
            .mb-3
              label.form-label(for='category') Category *
              select.form-select(id='category', name='category', required)
                option(value='') Select a category...
                each cat in categories
                  option(
                    value=cat._id,
                    selected=expense.category && expense.category.toString() === cat._id.toString()
                  )= cat.name
              .invalid-feedback Please select a category.
              if categories.length === 0
                .form-text.text-warning
                  | No categories available. 
                  a(href='/budget/category/create') Create one first.
            
            .d-flex.gap-2
              button.btn.btn-primary(type='submit', disabled=categories.length === 0) Save Expense
              a.btn.btn-secondary(href='/budget/expenses') Cancel
