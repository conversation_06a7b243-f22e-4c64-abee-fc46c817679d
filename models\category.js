const mongoose = require('mongoose');

const Schema = mongoose.Schema;

const CategorySchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxLength: 100
  },
  description: {
    type: String,
    trim: true,
    maxLength: 500
  },
  user: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Virtual for category's URL
CategorySchema.virtual('url').get(function () {
  return `/budget/category/${this._id}`;
});

// Virtual for category's edit URL
CategorySchema.virtual('edit_url').get(function () {
  return `/budget/category/${this._id}/edit`;
});

// Virtual for category's delete URL
CategorySchema.virtual('delete_url').get(function () {
  return `/budget/category/${this._id}/delete`;
});

// Index for efficient querying by user
CategorySchema.index({ user: 1, name: 1 });

// Export model
module.exports = mongoose.model('Category', CategorySchema);
