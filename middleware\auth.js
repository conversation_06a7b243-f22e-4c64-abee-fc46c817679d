// Authentication middleware functions

// Middleware to check if user is authenticated
exports.requireAuth = (req, res, next) => {
  if (req.session && req.session.user) {
    return next();
  } else {
    req.session.returnTo = req.originalUrl;
    res.redirect('/auth/login');
  }
};

// Middleware to redirect authenticated users away from auth pages
exports.redirectIfAuthenticated = (req, res, next) => {
  if (req.session && req.session.user) {
    return res.redirect('/budget/dashboard');
  }
  next();
};

// Middleware to get current user
exports.getCurrentUser = (req, res, next) => {
  if (req.session && req.session.user) {
    req.user = req.session.user;
    res.locals.currentUser = req.session.user;
  }
  next();
};
