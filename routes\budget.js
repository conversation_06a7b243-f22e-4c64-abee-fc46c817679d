const express = require('express');
const router = express.Router();

// Import controllers
const dashboardController = require('../controllers/dashboardController');
const categoryController = require('../controllers/categoryController');
const budgetController = require('../controllers/budgetController');
const expenseController = require('../controllers/expenseController');

// Import authentication middleware
const { requireAuth } = require('../middleware/auth');

// Apply authentication middleware to all budget routes
router.use(requireAuth);

/// DASHBOARD ROUTES ///

// GET dashboard home page
router.get('/', dashboardController.index);
router.get('/dashboard', dashboardController.index);

/// CATEGORY ROUTES ///

// GET request for creating a Category
router.get('/category/create', categoryController.category_create_get);

// POST request for creating Category
router.post('/category/create', categoryController.category_create_post);

// GET request to delete Category
router.get('/category/:id/delete', categoryController.category_delete_get);

// POST request to delete Category
router.post('/category/:id/delete', categoryController.category_delete_post);

// GET request to update Category
router.get('/category/:id/edit', categoryController.category_update_get);

// POST request to update Category
router.post('/category/:id/edit', categoryController.category_update_post);

// GET request for one Category
router.get('/category/:id', categoryController.category_detail);

// GET request for list of all Categories
router.get('/categories', categoryController.category_list);

/// BUDGET ROUTES ///

// GET request for creating a Budget
router.get('/budget/create', budgetController.budget_create_get);

// POST request for creating Budget
router.post('/budget/create', budgetController.budget_create_post);

// GET request to delete Budget
router.get('/budget/:id/delete', budgetController.budget_delete_get);

// POST request to delete Budget
router.post('/budget/:id/delete', budgetController.budget_delete_post);

// GET request to update Budget
router.get('/budget/:id/edit', budgetController.budget_update_get);

// POST request to update Budget
router.post('/budget/:id/edit', budgetController.budget_update_post);

// GET request for one Budget
router.get('/budget/:id', budgetController.budget_detail);

// GET request for list of all Budgets
router.get('/budgets', budgetController.budget_list);

/// EXPENSE ROUTES ///

// GET request for creating an Expense
router.get('/expense/create', expenseController.expense_create_get);

// POST request for creating Expense
router.post('/expense/create', expenseController.expense_create_post);

// GET request to delete Expense
router.get('/expense/:id/delete', expenseController.expense_delete_get);

// POST request to delete Expense
router.post('/expense/:id/delete', expenseController.expense_delete_post);

// GET request to update Expense
router.get('/expense/:id/edit', expenseController.expense_update_get);

// POST request to update Expense
router.post('/expense/:id/edit', expenseController.expense_update_post);

// GET request for one Expense
router.get('/expense/:id', expenseController.expense_detail);

// GET request for list of all Expenses
router.get('/expenses', expenseController.expense_list);

module.exports = router;
