/* Budget Tracker Custom Styles */

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.navbar-brand {
  font-weight: bold;
}

.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

.card-header {
  background-color: #fff;
  border-bottom: 1px solid #e9ecef;
  border-radius: 10px 10px 0 0 !important;
  font-weight: 600;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.form-control {
  border-radius: 6px;
  border: 1px solid #ced4da;
}

.form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.alert {
  border-radius: 8px;
  border: none;
}

.progress {
  height: 8px;
  border-radius: 4px;
}

.list-group-item {
  border: none;
  border-bottom: 1px solid #e9ecef;
  padding: 1rem 0;
}

.list-group-item:last-child {
  border-bottom: none;
}

.badge {
  font-size: 0.875rem;
}

/* Dashboard specific styles */
.dashboard-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.dashboard-card .card-body {
  padding: 1.5rem;
}

/* Chart containers */
.chart-container {
  position: relative;
  height: 300px;
  margin: 1rem 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .card-body {
    padding: 1rem;
  }
  
  .navbar-nav .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  
  .navbar-nav .dropdown-item {
    color: rgba(255, 255, 255, 0.75);
  }
  
  .navbar-nav .dropdown-item:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Form styles */
.form-floating {
  margin-bottom: 1rem;
}

.form-floating > .form-control {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}

.form-floating > label {
  padding: 1rem 0.75rem;
}

/* Table styles */
.table {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.table thead th {
  border-bottom: 2px solid #dee2e6;
  background-color: #f8f9fa;
  font-weight: 600;
}

/* Animation for loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom utility classes */
.text-currency {
  font-family: 'Courier New', monospace;
  font-weight: bold;
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #0d6efd 0%, #0b5ed7 100%);
}

.bg-gradient-success {
  background: linear-gradient(135deg, #198754 0%, #157347 100%);
}

.bg-gradient-warning {
  background: linear-gradient(135deg, #ffc107 0%, #ffca2c 100%);
}

.bg-gradient-info {
  background: linear-gradient(135deg, #0dcaf0 0%, #3dd5f3 100%);
}
